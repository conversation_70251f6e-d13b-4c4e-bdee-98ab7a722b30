package com.dcas.system.report;

import com.dcas.common.domain.entity.DynamicProcessTree;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.mapper.DynamicProcessTreeMapper;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.dto.TreeLabelDTO;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 可拆分报告的抽象基类
 * 提供按业务系统拆分报告的通用功能
 *
 * <AUTHOR>
 * @date 2025/09/02
 */
@Slf4j
public abstract class AbstractSplittableReport extends AbstractReport {

    @Autowired
    protected DynamicProcessTreeMapper dynamicProcessTreeMapper;

    /**
     * 获取作业的所有业务系统
     * @param operationId 作业ID
     * @return 业务系统列表
     */
    protected List<TreeLabelDTO> getBusinessSystems(String operationId) {
        return dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
    }

    /**
     * 检查是否需要按业务系统拆分
     * @param dto 导出参数
     * @param operationId 作业ID
     * @return 是否需要拆分
     */
    protected boolean shouldSplitByBusinessSystem(ExportWordDto dto, String operationId) {
        // 如果参数为null或false，不拆分
        if (dto.getSplitByBusinessSystem() == null || !dto.getSplitByBusinessSystem()) {
            return false;
        }

        // 获取业务系统列表
        List<TreeLabelDTO> businessSystems = getBusinessSystems(operationId);
        // 只有当存在多个业务系统时才拆分，单个或无业务系统时保持原有逻辑
        return businessSystems != null && businessSystems.size() > 1;
    }

    /**
     * 默认实现：支持按业务系统拆分的报告处理
     */
    @Override
    public List<String> processWithSplit(ExportWordDto dto, QueryProjectOperationExportVo poVo, Long modelId) throws Exception {
        List<String> filePaths = new ArrayList<>();

        try {
            if (!shouldSplitByBusinessSystem(dto, dto.getOperationId())) {
                // 不需要拆分，使用原有逻辑，确保完全兼容
                log.debug("不需要按业务系统拆分，使用原有导出逻辑");
                String filePath = process(dto, poVo, modelId);
                if (filePath != null) {
                    filePaths.add(filePath);
                }
                return filePaths;
            }

            // 需要拆分，按业务系统生成多个报告
            List<TreeLabelDTO> businessSystems = getBusinessSystems(dto.getOperationId());
            log.info("按业务系统拆分报告，作业ID: {}, 共{}个业务系统", dto.getOperationId(), businessSystems.size());

            // 如果获取业务系统失败，回退到原有逻辑
            if (businessSystems == null || businessSystems.isEmpty()) {
                log.warn("未找到业务系统信息，回退到原有导出逻辑");
                String filePath = process(dto, poVo, modelId);
                if (filePath != null) {
                    filePaths.add(filePath);
                }
                return filePaths;
            }

            for (TreeLabelDTO businessSystem : businessSystems) {
                try {
                    String filePath = processForBusinessSystem(dto, poVo, modelId, businessSystem);
                    if (filePath != null) {
                        filePaths.add(filePath);
                        log.info("生成业务系统[{}]报告成功: {}", businessSystem.getTreeName(), filePath);
                    }
                } catch (Exception e) {
                    log.error("生成业务系统[{}]报告失败，继续处理其他系统", businessSystem.getTreeName(), e);
                    // 不抛出异常，继续处理其他业务系统
                }
            }

            // 如果所有业务系统报告都失败了，回退到原有逻辑
            if (filePaths.isEmpty()) {
                log.warn("所有业务系统报告生成失败，回退到原有导出逻辑");
                String filePath = process(dto, poVo, modelId);
                if (filePath != null) {
                    filePaths.add(filePath);
                }
            }

        } catch (Exception e) {
            log.error("业务系统拆分处理失败，回退到原有导出逻辑", e);
            // 发生任何异常时，回退到原有逻辑
            String filePath = process(dto, poVo, modelId);
            if (filePath != null) {
                filePaths.clear();
                filePaths.add(filePath);
            }
        }

        return filePaths;
    }

    /**
     * 为特定业务系统生成报告
     * 子类需要实现此方法来处理特定业务系统的报告生成
     * 
     * @param dto 导出参数
     * @param poVo 作业信息
     * @param modelId 模板ID
     * @param businessSystem 业务系统信息
     * @return 生成的文件路径
     * @throws Exception 处理异常
     */
    protected abstract String processForBusinessSystem(ExportWordDto dto, QueryProjectOperationExportVo poVo, 
                                                     Long modelId, TreeLabelDTO businessSystem) throws Exception;

    /**
     * 生成带业务系统名称的文件名
     * @param originalFileName 原始文件名
     * @param businessSystemName 业务系统名称
     * @return 新的文件名
     */
    protected String generateBusinessSystemFileName(String originalFileName, String businessSystemName) {
        if (originalFileName.contains(".")) {
            int lastDotIndex = originalFileName.lastIndexOf(".");
            String nameWithoutExtension = originalFileName.substring(0, lastDotIndex);
            String extension = originalFileName.substring(lastDotIndex);
            return String.format("%s-%s%s", nameWithoutExtension, businessSystemName, extension);
        } else {
            return String.format("%s-%s", originalFileName, businessSystemName);
        }
    }

    /**
     * 过滤业务系统相关的数据
     * 子类可以使用此方法来过滤只属于特定业务系统的数据
     * 
     * @param dataList 原始数据列表
     * @param businessSystemName 业务系统名称
     * @param systemFieldName 数据中业务系统字段名称
     * @return 过滤后的数据列表
     */
    protected <T> List<T> filterByBusinessSystem(List<T> dataList, String businessSystemName, 
                                               java.util.function.Function<T, String> systemFieldExtractor) {
        if (dataList == null || dataList.isEmpty()) {
            return new ArrayList<>();
        }
        
        return dataList.stream()
                .filter(item -> businessSystemName.equals(systemFieldExtractor.apply(item)))
                .collect(Collectors.toList());
    }
}
